"use client";
import { useState, useEffect } from "react";
import BridgeInterface from "@/components/BridgeInterface";
import Providers from "@/components/Providers";

interface ChillhousePosition {
  id: number;
  top: number;
  left: number;
  size: number;
  rotation: number;
}

// Function to check if two positions overlap


// Function to generate aesthetically pleasing chillhouse positions
const generateChillhousePositions = (count: number): ChillhousePosition[] => {
  const positions: ChillhousePosition[] = [];
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

  // Create a grid-based approach for better distribution
  const gridCols = isMobile ? 5 : 7;
  const gridRows = Math.ceil(count / gridCols);

  // Size ranges for variety
  const baseSizeMin = isMobile ? 4 : 6;
  const baseSizeMax = isMobile ? 8 : 12;

  // Create grid cells and shuffle them for randomness
  const gridCells: { row: number; col: number }[] = [];
  for (let row = 0; row < gridRows; row++) {
    for (let col = 0; col < gridCols; col++) {
      gridCells.push({ row, col });
    }
  }

  // Shuffle the grid cells for random placement
  for (let i = gridCells.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [gridCells[i], gridCells[j]] = [gridCells[j], gridCells[i]];
  }

  // Place chillhouses in shuffled grid positions
  for (let i = 0; i < Math.min(count, gridCells.length); i++) {
    const cell = gridCells[i];

    // Calculate cell boundaries with some overlap allowance
    const cellWidth = 100 / gridCols;
    const cellHeight = 100 / gridRows;

    // Add randomness within each cell (±30% of cell size)
    const randomOffsetX = (Math.random() - 0.5) * cellWidth * 0.6;
    const randomOffsetY = (Math.random() - 0.5) * cellHeight * 0.6;

    // Calculate position with randomness
    const baseLeft = (cell.col * cellWidth) + (cellWidth / 2);
    const baseTop = (cell.row * cellHeight) + (cellHeight / 2);

    const position: ChillhousePosition = {
      id: i,
      left: Math.max(0, Math.min(95, baseLeft + randomOffsetX)),
      top: Math.max(0, Math.min(95, baseTop + randomOffsetY)),
      size: baseSizeMin + Math.random() * (baseSizeMax - baseSizeMin),
      rotation: Math.random() * 360 - 180,
    };

    positions.push(position);
  }

  return positions;
};

export default function Home() {
  const [isClient, setIsClient] = useState(false);
  const [chillhousePositions, setChillhousePositions] = useState<ChillhousePosition[]>([]);

  useEffect(() => {
    setIsClient(true);
    // Generate random chillhouse positions on client load
    setChillhousePositions(generateChillhousePositions(35));

    // Regenerate positions on window resize to handle mobile/desktop transitions
    const handleResize = () => {
      setChillhousePositions(generateChillhousePositions(35));
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  if (!isClient) return null;

  return (
    <Providers>
      <div className="min-h-screen bg-chillhouse-bg flex items-center justify-center py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        {/* Random Chillhouse Characters Background */}
        <div className="absolute inset-0 z-0">
          {chillhousePositions.map((position) => (
            <img
              key={position.id}
              src="/chillhouse.webp"
              alt="Chill House Character"
              className="absolute opacity-12 h-auto"
              style={{
                top: `${position.top}%`,
                left: `${position.left}%`,
                width: `${position.size}rem`,
                transform: `rotate(${position.rotation}deg)`,
              }}
            />
          ))}
        </div>

        {/* Main Content */}
        <div className="w-full relative z-30">
          <BridgeInterface />
        </div>
      </div>
    </Providers>
  );
}
