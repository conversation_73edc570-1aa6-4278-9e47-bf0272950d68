"use client";
import { useState, useEffect } from "react";
import BridgeInterface from "@/components/BridgeInterface";
import Providers from "@/components/Providers";

interface ChillhousePosition {
  id: number;
  top: number;
  left: number;
  size: number;
  rotation: number;
}

// Function to check if two positions overlap
const checkOverlap = (pos1: ChillhousePosition, pos2: ChillhousePosition): boolean => {
  const buffer = 5; // 5% buffer to ensure no overlap
  const pos1Right = pos1.left + pos1.size + buffer;
  const pos1Bottom = pos1.top + pos1.size + buffer;
  const pos2Right = pos2.left + pos2.size + buffer;
  const pos2Bottom = pos2.top + pos2.size + buffer;

  return !(pos1Right < pos2.left || pos2Right < pos1.left ||
    pos1Bottom < pos2.top || pos2Bottom < pos1.top);
};

// Function to generate random chillhouse positions without overlap
const generateChillhousePositions = (count: number): ChillhousePosition[] => {
  const positions: ChillhousePosition[] = [];
  const maxAttempts = 100; // Prevent infinite loops

  // Adjust count and size based on screen size
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;
  const adjustedCount = isMobile ? Math.min(count, 8) : count; // Fewer images on mobile
  const baseSizeMin = isMobile ? 8 : 12; // Smaller base size on mobile
  const baseSizeMax = isMobile ? 12 : 20; // Smaller max size on mobile

  for (let i = 0; i < adjustedCount; i++) {
    let attempts = 0;
    let newPosition: ChillhousePosition;

    do {
      newPosition = {
        id: i,
        top: Math.random() * 80, // 0-80% to avoid edges (more conservative)
        left: Math.random() * 80, // 0-80% to avoid edges (more conservative)
        size: baseSizeMin + Math.random() * (baseSizeMax - baseSizeMin),
        rotation: Math.random() * 360 - 180, // -180 to 180 degrees
      };
      attempts++;
    } while (
      attempts < maxAttempts &&
      positions.some(pos => checkOverlap(pos, newPosition))
    );

    if (attempts < maxAttempts) {
      positions.push(newPosition);
    }
  }

  return positions;
};

export default function Home() {
  const [isClient, setIsClient] = useState(false);
  const [chillhousePositions, setChillhousePositions] = useState<ChillhousePosition[]>([]);

  useEffect(() => {
    setIsClient(true);
    // Generate random chillhouse positions on client load
    setChillhousePositions(generateChillhousePositions(15));

    // Regenerate positions on window resize to handle mobile/desktop transitions
    const handleResize = () => {
      setChillhousePositions(generateChillhousePositions(15));
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  if (!isClient) return null;

  return (
    <Providers>
      <div className="min-h-screen bg-chillhouse-bg flex items-center justify-center py-32 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        {/* Random Chillhouse Characters Background */}
        <div className="absolute inset-0 z-0">
          {chillhousePositions.map((position) => (
            <img
              key={position.id}
              src="/chillhouse.webp"
              alt="Chill House Character"
              className="absolute opacity-12 h-auto"
              style={{
                top: `${position.top}%`,
                left: `${position.left}%`,
                width: `${position.size}rem`,
                transform: `rotate(${position.rotation}deg)`,
              }}
            />
          ))}
        </div>

        {/* Main Content */}
        <div className="w-full relative z-30">
          <BridgeInterface />
        </div>
      </div>
    </Providers>
  );
}
